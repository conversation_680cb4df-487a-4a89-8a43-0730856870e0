<svg width="461" height="355" viewBox="0 0 461 355" fill="none" xmlns="http://www.w3.org/2000/svg">
<foreignObject x="0" y="0" width="461" height="355"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2px);clip-path:url(#bgblur_0_48_1033_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_d_48_1033)" data-figma-bg-blur-radius="4">
<path d="M32 40C32 28.9543 40.9543 20 52 20H409C420.046 20 429 28.9543 429 40V291C429 302.046 420.046 311 409 311H52C40.9543 311 32 302.046 32 291V40Z" fill="white" fill-opacity="0.3" shape-rendering="crispEdges"/>
</g>
<defs>
<filter id="filter0_d_48_1033" x="0" y="0" width="461" height="355" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="12"/>
<feGaussianBlur stdDeviation="16"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.599359 0 0 0 0 0.67281 0 0 0 0 1 0 0 0 0.14 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_48_1033"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_48_1033" result="shape"/>
</filter>
<clipPath id="bgblur_0_48_1033_clip_path" transform="translate(0 0)"><path d="M32 40C32 28.9543 40.9543 20 52 20H409C420.046 20 429 28.9543 429 40V291C429 302.046 420.046 311 409 311H52C40.9543 311 32 302.046 32 291V40Z"/>
</clipPath></defs>
</svg>
