/* 联系我们 */
.contact {
  padding: 80px 0 120px;
  text-align: center;
  background: linear-gradient(135deg, #f5f7ff 0%, #e8f0ff 100%);
  position: relative;
  overflow: hidden;
}

.contact::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("./images/foot/lianxi.svg") no-repeat center center,
    radial-gradient(
      ellipse 800px 300px at 50% 50%,
      rgba(24, 162, 242, 0.08) 0%,
      transparent 70%
    ),
    linear-gradient(
      135deg,
      rgba(24, 162, 242, 0.03) 0%,
      rgba(23, 85, 181, 0.02) 100%
    );
  background-size: cover, auto, auto;
  pointer-events: none;
}

.contact h2 {
  font-size: 36px;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 50px;
  position: relative;
  z-index: 1;
}

.contact-grid {
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  width: 100%;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.contact-card {
  box-sizing: border-box;
  background: url("./images/foot/1.svg") no-repeat;
  background-size: 100% 100%;
  padding: 50px 30px;
  text-align: center;
  flex: 1;
  position: relative;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.contact-card:first-child {
  border-radius: 20px 0 0 20px;
}

.contact-card:last-child {
  border-radius: 0 20px 20px 0;
}

.contact-card:not(:first-child):not(:last-child) {
  border-radius: 0;
}

.contact-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(24, 162, 242, 0.15);
}

.contact-card:last-child::after {
  display: none;
}

.contact-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.contact-icon img {
  width: 80px;
  height: 80px;
}

.contact-card h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 12px;
}

.contact-card p {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  margin: 0;
}
/* 页脚 */
.footer {
  background: url("./images/foot/foot1.svg") no-repeat;
  background-size: 100% 100%;
  color: #ffffff;
  padding: 60px 0 30px;
  position: relative;
  overflow: hidden;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  gap: 60px;
  position: relative;
  z-index: 1;
}

.footer-logo {
  flex: 0 0 200px;
}

.footer-logo img {
  width: 120px;
  height: auto;
  margin-bottom: 20px;
}

.footer-sections {
  display: flex;
  gap: 80px;
  flex: 1;
}

.footer-section h4 {
  font-size: 14px;
  font-weight: 600;
  background: linear-gradient(to right, #18a2f2, #1755b5);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 20px;
  position: relative;
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section ul li {
  margin-bottom: 12px;
}

.footer-section ul li a {
  color: #a8b3d1;
  text-decoration: none;
  font-size: 13px;
  transition: color 0.3s ease;
}

.footer-section ul li a:hover {
  color: #18a2f2;
}

.contact-info {
  min-width: 200px;
}

.contact-info p {
  margin-bottom: 12px;
  font-size: 13px;
  color: #a8b3d1;
  display: flex;
  align-items: center;
  gap: 8px;
}

.contact-info p::before {
  content: "";
  width: 16px;
  height: 16px;
  background-size: contain;
  background-repeat: no-repeat;
  flex-shrink: 0;
}

.contact-info p.phone::before {
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" fill="%2318a2f2" viewBox="0 0 24 24"><path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 ********** 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 **********.03.74-.25 1.02l-2.2 2.2z"/></svg>');
}

.contact-info p.address::before {
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" fill="%2318a2f2" viewBox="0 0 24 24"><path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/></svg>');
}

.social-media {
  display: flex;
  gap: 30px;
  align-items: flex-start;
  flex: 0 0 auto;
}

.qr-code {
  text-align: center;
}

.qr-code img {
  width: 80px;
  height: 80px;
  margin-bottom: 8px;
  border-radius: 8px;
  border: 2px solid rgba(255, 255, 255, 0.1);
}

.qr-code p {
  font-size: 12px;
  color: #a8b3d1;
  margin: 0;
}

.footer-bottom {
  text-align: center;
  padding-top: 30px;
  margin-top: 40px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  z-index: 1;
}

.footer-bottom p {
  font-size: 13px;
  color: #7a8bb5;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .contact {
    padding: 60px 20px 80px;
  }

  .contact h2 {
    font-size: 28px;
    margin-bottom: 40px;
  }

  .contact-grid {
    flex-direction: column;
    max-width: 400px;
  }

  .contact-card {
    border-radius: 20px !important;
    margin-bottom: 20px;
  }

  .contact-card::after {
    display: none;
  }

  .footer-content {
    flex-direction: column;
    gap: 40px;
    padding: 0 20px;
  }

  .footer-sections {
    flex-direction: column;
    gap: 40px;
  }

  .social-media {
    justify-content: center;
    gap: 20px;
  }
}

@media (max-width: 480px) {
  .contact-icon {
    width: 60px;
    height: 60px;
  }

  .contact-icon img {
    width: 30px;
    height: 30px;
  }

  .contact-card {
    padding: 40px 20px;
  }

  .footer {
    padding: 40px 0 20px;
  }

  .footer-content {
    padding: 0 15px;
  }
}
