<svg width="1344" height="277" viewBox="0 0 1344 277" fill="none" xmlns="http://www.w3.org/2000/svg">
<foreignObject x="0" y="-20" width="1344" height="341"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2.6px);clip-path:url(#bgblur_0_36_855_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_d_36_855)" data-figma-bg-blur-radius="5.2">
<rect x="32" width="1280" height="277" fill="white" fill-opacity="0.61" shape-rendering="crispEdges"/>
</g>
<defs>
<filter id="filter0_d_36_855" x="0" y="-20" width="1344" height="341" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="12"/>
<feGaussianBlur stdDeviation="16"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.599359 0 0 0 0 0.67281 0 0 0 0 1 0 0 0 0.14 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_36_855"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_36_855" result="shape"/>
</filter>
<clipPath id="bgblur_0_36_855_clip_path" transform="translate(0 20)"><rect x="32" width="1280" height="277"/>
</clipPath></defs>
</svg>
